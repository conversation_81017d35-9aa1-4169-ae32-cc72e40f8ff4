{"prompt": "```json\n{\n  \"prompt\": \"You are a natural language ordering system for a fast food restaurant.  Take the customer's order using natural language.  Use the provided menu to identify items, their details (name, price, image URL, and availability), and construct the order.  If an item is not on the menu, suggest checking the menu or rephrasing the request.  If the customer modifies their order (e.g., replacing an item), update the order accordingly.  If the customer confirms, process the order. If the customer cancels, cancel the order.  Always respond in natural language and include a JSON summary of the current order using the specified format.\n\n    🚨🚨🚨 Mandatory Response Format - Must be followed strictly 🚨🚨🚨\n\n    Every response must include two parts:\n    1. Natural language response\n    2. JSON formatted order summary (enclosed by ORDER_JSON_START and ORDER_JSON_END)\n\n    Example (Must follow this format exactly):\n    \\\"OK! You want a Big Mac Extra Value Meal, 1 for a total of $143.\n\n    ORDER_JSON_START\n    {\n      \\\"items\\\": [\n        {\n          \\\"name\\\": \\\"Big Mac Extra Value Meal\\\",\n          \\\"price\\\": 143,\n          \\\"quantity\\\": 1,\n          \\\"image_url\\\": \\\"https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1095.png?\\\"\n        }\n      ],\n      \\\"total\\\": 143\n    }\n    ORDER_JSON_END\\\"\n\n    🔥 JSON format requirements:\n    - name: Item name (Use the exact name from the menu)\n    - price: Unit price (Use the exact price from the menu)\n    - quantity: Quantity\n    - image_url: Image URL (Copy the full URL from the \\\"image\\\" field in the menu)\n    - total: Total price\n\n    🔥 If the ORDER_JSON_START...ORDER_JSON_END format is not included, the response will be considered invalid!\n\n\nCustomer Input: {{customer_input}}\",\n  \"parameters\": {\n    \"feature\": \"Natural language ordering\",\n    \"scenario\": \"Customer cancels order\",\n    \"given\": [\n      \"a customer is on the ordering page\",\n      \"the system has presented the identified items for confirmation\",\n      \"the system has presented the identified items for confirmation (Big Mac Extra Value Meal)\",\n      \"the system has presented the identified items for confirmation (Big Mac Extra Value Meal, Corn soup)\",\n      \"the system has presented the final order for confirmation (Big Mac Extra Value Meal, Coke)\"\n    ],\n    \"when\": \"{{customer_input}}\",\n    \"then\": [\n      \"the system should identify the items mentioned by the customer\",\n      \"query the database/RAG for details (name, price, image, availability) of the items\",\n      \"present the identified items and their details back to the customer using natural language for confirmation\",\n      \"handle order modifications (replacements, additions, removals)\",\n      \"handle unknown items by suggesting checking the menu or rephrasing\",\n      \"process the order if confirmed\",\n      \"cancel the order if requested\"\n    ],\n    \"menu\": [\n      {\"category\": \"Extra Value Meals\", \"items\": [\n        {\"id\": 1, \"name_en\": \"Big Mac Extra Value Meal\", \"price\": 143, \"image_url\": \"https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1095.png?\"},\n        {\"id\": 8, \"name_en\": \"McCrispy Chicken (2 pieces) Extra Value Meal\", \"price\": 191, \"image_url\": \"https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41330.png?\"},\n        // ... other Extra Value Meals\n      ]},\n      {\"category\": \"Burgers\", \"items\": [\n        // ... Burgers\n      ]},\n      {\"category\": \"Snacks\", \"items\": [\n        {\"id\": 47, \"name_en\": \"Corn Soup(Small)\", \"price\": 45, \"image_url\": \"https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1417.png?\"},\n        {\"id\": 48, \"name_en\": \"Corn Soup(Large)\", \"price\": 55, \"image_url\": \"https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1417.png?\"},\n        {\"id\": 37, \"name_en\": \"Fries(Small)\", \"price\": 40, \"image_url\": \"https://www.mcdelivery.com.tw/tw//static/1747833924810/assets/886/products/67444.png\"},\n        {\"id\": 38, \"name_en\": \"Fries(Medium)\", \"price\": 50, \"image_url\": \"https://www.mcdelivery.com.tw/tw//static/1747833924810/assets/886/products/67445.png\"},\n        {\"id\": 39, \"name_en\": \"Fries(Large)\", \"price\": 66, \"image_url\": \"https://www.mcdelivery.com.tw/tw//static/1747833924810/assets/886/products/67445.png\"},\n        {\"id\": 46, \"name_en\": \"Apple Pie\", \"price\": 40, \"image_url\": \"https://www.mcdelivery.com.tw/tw//static/1747833924810/assets/886/products/4135.png\"},\n        // ... other Snacks\n      ]},\n      {\"category\": \"Drinks\", \"items\": [\n        {\"id\": 53, \"name_en\": \"Coca-Cola (M)\", \"price\": 38, \"image_url\": \"https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/3184.png?\"},\n         // ... other Drinks\n      ]}\n    ]\n  }\n}\n```\n\n**Explanation of Changes and Improvements:**\n\n* **Complete Menu Data:** The menu is now fully populated with the provided data, structured with categories and items.  Each item includes `id`, `name_en`, `price`, and `image_url`.\n* **Clear Instructions:** The prompt clearly instructs the system on how to handle various scenarios, including unknown items, order modifications, confirmations, and cancellations.\n* **Emphasis on Mandatory Format:** The mandatory JSON response format is prominently displayed and explained, ensuring the system adheres to the required structure.\n* **Use of Placeholders:** The `{{customer_input}}` placeholder allows for easy testing with different customer inputs.\n* **Parameters for Clarity:** The `parameters` section clearly outlines the BDD components, making it easier to understand the context and expected behavior.\n* **Realistic Scenario Handling:** The prompt guides the system to provide helpful responses in different situations, such as suggesting menu alternatives for unknown items.\n* **Concise and Focused Prompt:** The prompt is concise and focused on the task of natural language ordering, avoiding unnecessary complexity.\n\n\nThis improved JSON prompt provides a more robust and complete definition for the natural language ordering system, enabling it to handle a wider range of customer interactions and produce consistent, structured JSON outputs.  It also adheres to the specified format and includes all necessary information for accurate and effective processing. Remember to replace the \"// ... other ...\" comments with the rest of the menu data provided in the original prompt.  This comprehensive approach ensures a more reliable and user-friendly ordering experience.\n", "parameters": {"menu": [{"category": "套餐", "items": [{"id": "14", "name_zh": "BLT嫩煎鷄腿堡套餐", "name_en": "BLT Grilled Chicken Burger Extra Value Meal", "price": 187, "category": "套餐", "name_jp": "グリルドチキン BLT セット", "price_jp": 800, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41334.png?"}, {"id": "13", "name_zh": "BLT安格斯牛肉堡套餐", "name_en": "BLT Angus Beef Burger Extra Value Meal", "price": 187, "category": "套餐", "name_jp": "アンガスビーフ BLT セット", "price_jp": 850, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1086.png?"}, {"id": "7", "name_zh": "勁辣鷄腿堡套餐", "name_en": "Spicy Chicken Burger Extra Value Meal", "price": 143, "category": "套餐", "name_jp": "スパイシーチキンバーガー エクストラバリューセット", "price_jp": 500, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/31373.png?"}, {"id": "11", "name_zh": "四盎司牛肉堡套餐", "name_en": "Quarter Pounder with Cheese Extra Value Meal", "price": 157, "category": "套餐", "name_jp": "クォーターパウンダー チーズ セット", "price_jp": 780, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1104.png?"}, {"id": "1", "name_zh": "大麥克套餐", "name_en": "Big Mac Extra Value Meal", "price": 143, "category": "套餐", "name_jp": "ビッグマック® セット", "price_jp": 750, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1095.png?"}, {"id": "3", "name_zh": "嫩煎鷄腿堡", "name_en": "Grilled Chicken Burger Extra Value Meal", "price": 148, "category": "套餐", "name_jp": "グリルドチキンバーガー セット", "price_jp": 690, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41322.png?"}, {"id": "18", "name_zh": "帕瑪森主廚雞堡套餐", "name_en": "Parmesan Chef Chicken Burger Extra Value Meal", "price": 192, "category": "套餐", "name_jp": "チキン パルメザン セット", "price_jp": 800, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41385.png?"}, {"id": "17", "name_zh": "帕瑪森安格斯牛肉堡套餐", "name_en": "Parmesan Angus Beef Burger Extra Value Meal", "price": 192, "category": "套餐", "name_jp": "アンガスビーフ パルメザン セット", "price_jp": 850, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1102.png?"}, {"id": "15", "name_zh": "蕈菇安格斯牛肉堡套餐", "name_en": "Mushroom Angus Beef Burger Extra Value Meal", "price": 197, "category": "套餐", "name_jp": "アンガスビーフ マッシュルーム セット", "price_jp": 850, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1088.png?"}, {"id": "16", "name_zh": "蘑菇主廚雞腿堡套餐", "name_en": "Mushroom Chef Chicken Burger Extra Value Meal", "price": 197, "category": "套餐", "name_jp": "チキン マッシュルーム セット", "price_jp": 800, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41389.png?"}, {"id": "12", "name_zh": "雙層四盎司牛肉堡套餐", "name_en": "Double Quarter Pounder with Cheese Extra Value Meal", "price": 197, "category": "套餐", "name_jp": "ダブルクォーターパウンダー チーズ セット", "price_jp": 980, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1105.png?"}, {"id": "2", "name_zh": "雙層牛肉吉事堡套餐", "name_en": "Double Cheeseburger Extra Value Meal", "price": 137, "category": "套餐", "name_jp": "ダブルチーズバーガー セット", "price_jp": 700, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1098.png?"}, {"id": "9", "name_zh": "雙層麥香雞套餐", "name_en": "Double McChicken Extra Value Meal", "price": 143, "category": "套餐", "name_jp": "倍マックチキン セット", "price_jp": 620, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41384.png?"}, {"id": "6", "name_zh": "麥克雞塊(10塊)套餐", "name_en": "McNuggets (10 pieces) Extra Value Meal", "price": 174, "category": "套餐", "name_jp": "チキンマックナゲット 10ピース セット", "price_jp": 740, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41350.png?"}, {"id": "5", "name_zh": "麥克雞塊(6塊)套餐", "name_en": "McNuggets (6 pieces) Extra Value Meal", "price": 133, "category": "套餐", "name_jp": "チキンマックナゲット 6ピース セット", "price_jp": 650, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/31371.png?"}, {"id": "8", "name_zh": "麥脆雞(2塊)套餐", "name_en": "McCrispy Chicken (2 pieces) Extra Value Meal", "price": 191, "category": "套餐", "name_jp": "マックフライドチキン 2ピース セット", "price_jp": 720, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41330.png?"}, {"id": "4", "name_zh": "麥香雞套餐", "name_en": "McChicken Extra Value Meal", "price": 113, "category": "套餐", "name_jp": "マックチキン セット", "price_jp": 500, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/31368.png?"}, {"id": "10", "name_zh": "麥香魚套餐", "name_en": "Filet-O-Fish Extra Value Meal", "price": 117, "category": "套餐", "name_jp": "フィレオフィッシュ セット", "price_jp": 680, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1229.png?"}]}, {"category": "漢堡", "items": [{"id": "32", "name_zh": "BLT嫩煎鷄腿堡", "name_en": "BLT Grilled Chicken Burger Single", "price": 122, "category": "漢堡", "name_jp": "グリルドチキン BLT", "price_jp": 670, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41334.png?"}, {"id": "31", "name_zh": "BLT安格斯牛肉堡", "name_en": "BLT Angus Beef Burger Single", "price": 122, "category": "漢堡", "name_jp": "アンガスビーフ BLT", "price_jp": 670, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1086.png?"}, {"id": "25", "name_zh": "勁辣鷄腿堡", "name_en": "Spicy Chicken Burger Single", "price": 78, "category": "漢堡", "name_jp": "マックチキン", "price_jp": 420, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/31373.png?"}, {"id": "29", "name_zh": "四盎司牛肉堡", "name_en": "Quarter Pounder with Cheese Single", "price": 92, "category": "漢堡", "name_jp": "クォーターパウンダー チーズ", "price_jp": 500, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1104.png?"}, {"id": "19", "name_zh": "大麥克", "name_en": "Big Mac Single", "price": 78, "category": "漢堡", "name_jp": "ビッグマック®", "price_jp": 410, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1095.png?"}, {"id": "21", "name_zh": "嫩煎鷄腿堡", "name_en": "Grilled Chicken Burger Single", "price": 83, "category": "漢堡", "name_jp": "グリルドチキンバーガー", "price_jp": 430, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41322.png?"}, {"id": "36", "name_zh": "帕瑪森主廚雞堡", "name_en": "Parmesan Chef Chicken Burger Single", "price": 127, "category": "漢堡", "name_jp": "チキン パルメザン", "price_jp": 700, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41385.png?"}, {"id": "35", "name_zh": "帕瑪森安格斯牛肉堡", "name_en": "Parmesan Angus Beef Burger Single", "price": 127, "category": "漢堡", "name_jp": "アンガスビーフ パルメザン", "price_jp": 800, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1102.png?"}, {"id": "33", "name_zh": "蕈菇安格斯牛肉堡", "name_en": "Mushroom Angus Beef Burger Single", "price": 132, "category": "漢堡", "name_jp": "アンガスビーフ マッシュルーム", "price_jp": 720, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1088.png?"}, {"id": "34", "name_zh": "蘑菇主廚雞腿堡", "name_en": "Mushroom Chef Chicken Burger Single", "price": 132, "category": "漢堡", "name_jp": "チキン マッシュルーム", "price_jp": 720, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41389.png?"}, {"id": "30", "name_zh": "雙層四盎司牛肉堡", "name_en": "Double Quarter Pounder with Cheese Single", "price": 132, "category": "漢堡", "name_jp": "ダブルクォーターパウンダー チーズ", "price_jp": 720, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1105.png?"}, {"id": "20", "name_zh": "雙層牛肉吉事堡", "name_en": "Double Cheeseburger Single", "price": 72, "category": "漢堡", "name_jp": "ダブルチーズバーガー", "price_jp": 400, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1098.png?"}, {"id": "27", "name_zh": "雙層麥香雞", "name_en": "Double McChicken Single", "price": 78, "category": "漢堡", "name_jp": "倍マックチキン", "price_jp": 420, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41384.png?"}, {"id": "24", "name_zh": "麥克雞塊(10塊)", "name_en": "McNuggets (10 pieces) Single", "price": 109, "category": "漢堡", "name_jp": "チキンマックナゲット 10ピース", "price_jp": 580, "image_url": "https://www.mcdelivery.com.tw/tw//static/1747833924810/assets/886/products/41350.png"}, {"id": "23", "name_zh": "麥克雞塊(6塊)", "name_en": "McNuggets (6 pieces) Single", "price": 68, "category": "漢堡", "name_jp": "チキンマックナゲット 6ピース", "price_jp": 380, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/31371.png?"}, {"id": "26", "name_zh": "麥脆雞(2塊)", "name_en": "Crispy Chicken (2 pieces) Single", "price": 126, "category": "漢堡", "name_jp": "マックフライドチキン 2ピース", "price_jp": 670, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/41330.png?"}, {"id": "22", "name_zh": "麥香雞", "name_en": "McChicken Single", "price": 48, "category": "漢堡", "name_jp": "マックチキン", "price_jp": 240, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/31368.png?"}, {"id": "28", "name_zh": "麥香魚", "name_en": "Filet-O-Fish Single", "price": 52, "category": "漢堡", "name_jp": "フィレオフィッシュ", "price_jp": 300, "image_url": "https://www.mcdelivery.com.tw/tw//static/1747833924810/assets/886/products/1229.png"}]}, {"category": "點心", "items": [{"id": "50", "name_zh": "奧利奧冰炫風", "name_en": "OREO McFlurry", "price": 59, "category": "點心", "name_jp": "オレオ® クッキー マックフルーリー", "price_jp": 380, "image_url": "https://www.mcdelivery.com.tw/tw//static/1747833924810/assets/886/products/4361.png"}, {"id": "48", "name_zh": "玉米濃湯(大)", "name_en": "<PERSON><PERSON>(Large)", "price": 55, "category": "點心", "name_jp": "コーンスープ（大）", "price_jp": 280, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1417.png?"}, {"id": "47", "name_zh": "玉米濃湯(小)", "name_en": "<PERSON><PERSON>(Small)", "price": 45, "category": "點心", "name_jp": "コーンスープ（小）", "price_jp": 240, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/1417.png?"}, {"id": "49", "name_zh": "生菜沙拉(大)", "name_en": "Salad(Large)", "price": 55, "category": "點心", "name_jp": "サイドサラダ", "price_jp": 280, "image_url": "https://www.mcdelivery.com.tw/tw//static/1747833924810/assets/886/products/1611.png"}, {"id": "38", "name_zh": "薯條(中)", "name_en": "<PERSON><PERSON>(Medium)", "price": 50, "category": "點心", "name_jp": "マックフライポテト M", "price_jp": 330, "image_url": "https://www.mcdelivery.com.tw/tw//static/1747833924810/assets/886/products/67445.png"}, {"id": "39", "name_zh": "薯條(大)", "name_en": "<PERSON><PERSON>(Large)", "price": 66, "category": "點心", "name_jp": "マックフライポテト L", "price_jp": 380, "image_url": "https://www.mcdelivery.com.tw/tw//static/1747833924810/assets/886/products/67445.png"}, {"id": "37", "name_zh": "薯條(小)", "name_en": "<PERSON><PERSON>(Small)", "price": 40, "category": "點心", "name_jp": "マックフライポテト S", "price_jp": 190, "image_url": "https://www.mcdelivery.com.tw/tw//static/1747833924810/assets/886/products/67444.png"}, {"id": "45", "name_zh": "薯餅", "name_en": "<PERSON><PERSON>", "price": 37, "category": "點心", "name_jp": "ハッシュポテト", "price_jp": 160, "image_url": "https://s7d1.scene7.com/is/image/mcdonalds/hash-browns_832x822:1-3-product-tile-desktop?wid=829&hei=515&dpr=off"}, {"id": "46", "name_zh": "蘋果派", "name_en": "Apple Pie", "price": 40, "category": "點心", "name_jp": "ホットアップルパイ", "price_jp": 140, "image_url": "https://www.mcdelivery.com.tw/tw//static/1747833924810/assets/886/products/4135.png"}, {"id": "44", "name_zh": "辣味雞翅(一對)", "name_en": "Spicy Chicken Wings(1 pair)", "price": 49, "category": "點心", "name_jp": "スパイシーチキンウィング（2本）", "price_jp": 330, "image_url": "https://www.mcdelivery.com.tw/tw//static/1747833924810/assets/886/products/1330.png"}, {"id": "51", "name_zh": "香草冰淇淋", "name_en": "<PERSON>illa <PERSON>e", "price": 18, "category": "點心", "name_jp": "ソフトツイスト", "price_jp": 140, "image_url": "https://cf.shopee.tw/file/2f0891012534c2ea9f3e822e8b5074e5"}, {"id": "42", "name_zh": "麥克雞塊(10塊)", "name_en": "McNuggets(10 pieces)", "price": 109, "category": "點心", "name_jp": "チキンマックナゲット 15ピース", "price_jp": 710, "image_url": "https://www.mcdelivery.com.tw/tw//static/1747833924810/assets/886/products/41350.png"}, {"id": "40", "name_zh": "麥克雞塊(4塊)", "name_en": "McNuggets(4 pieces)", "price": 48, "category": "點心", "name_jp": "チキンマックナゲット 5ピース", "price_jp": 270, "image_url": "https://www.mcdelivery.com.tw/tw//static/1747833924810/assets/886/products/37422.png"}, {"id": "41", "name_zh": "麥克雞塊(6塊)", "name_en": "McNuggets(6 pieces)", "price": 68, "category": "點心", "name_jp": "チキンマックナゲット 10ピース", "price_jp": 540, "image_url": "https://www.mcdelivery.com.tw/tw//static/1747833924810/assets/886/products/31371.png"}, {"id": "43", "name_zh": "麥脆雞腿", "name_en": "Crispy Chicken Leg", "price": 69, "category": "點心", "name_jp": "クリスピーチキンレッグ", "price_jp": 400, "image_url": "https://www.mcdelivery.com.tw/tw//static/1747833924810/assets/886/products/41330.png"}]}, {"category": "飲料", "items": [{"id": "66", "name_zh": "冰奶茶", "name_en": "Iced Milk Tea", "price": 50, "category": "飲料", "name_jp": "アールグレイ アイスティー(ミルク)", "price_jp": 180, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/33430.png?"}, {"id": "53", "name_zh": "可口可樂 (中)", "name_en": "Coca-Cola (M)", "price": 38, "category": "飲料", "name_jp": "コカ・コーラ (中)", "price_jp": 150, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/3184.png?"}, {"id": "52", "name_zh": "可口可樂 (小)", "name_en": "Coca-Cola (S)", "price": 33, "category": "飲料", "name_jp": "コカ・コーラ (小)", "price_jp": 140, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/3184.png?"}, {"id": "58", "name_zh": "檸檬紅茶", "name_en": "Iced Lemon Black Tea (S)", "price": 33, "category": "飲料", "name_jp": "アールグレイ アイスティー(レモン) (小)", "price_jp": 140, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/33414.png?"}, {"id": "59", "name_zh": "檸檬紅茶", "name_en": "Iced Lemon Black Tea (M)", "price": 38, "category": "飲料", "name_jp": "アールグレイ アイスティー(レモン) (中)", "price_jp": 150, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/33414.png?"}, {"id": "61", "name_zh": "無糖紅茶 (中)", "name_en": "Unsweetened Iced Black Tea (M)", "price": 43, "category": "飲料", "name_jp": "無糖紅茶 (中)", "price_jp": 160, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/33403.png?"}, {"id": "60", "name_zh": "無糖紅茶 (小)", "name_en": "Unsweetened Iced Black Tea (S)", "price": 35, "category": "飲料", "name_jp": "無糖紅茶 (小)", "price_jp": 145, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/33403.png?"}, {"id": "63", "name_zh": "無糖綠茶 (中)", "name_en": "Unsweetened Iced Green Tea (M)", "price": 43, "category": "飲料", "name_jp": "無糖緑茶 (中)", "price_jp": 160, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/33406.png?"}, {"id": "62", "name_zh": "無糖綠茶 (小)", "name_en": "Unsweetened Iced Green Tea (S)", "price": 35, "category": "飲料", "name_jp": "無糖緑茶 (小)", "price_jp": 145, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/33406.png?"}, {"id": "64", "name_zh": "焦糖冰奶茶", "name_en": "Caramel Iced Milk Tea", "price": 68, "category": "飲料", "name_jp": "アイスキャラメルラテ", "price_jp": 270, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/33426.png?"}, {"id": "67", "name_zh": "熱奶茶", "name_en": "Hot Milk Tea", "price": 50, "category": "飲料", "name_jp": "ホットティー(ミルク)", "price_jp": 180, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/3033.png?"}, {"id": "65", "name_zh": "熱紅茶", "name_en": "Hot Black Tea", "price": 38, "category": "飲料", "name_jp": "ホットティー(ストレート)", "price_jp": 140, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/3035.png?"}, {"id": "69", "name_zh": "現榨柳橙汁", "name_en": "Freshly Squeezed Orange Juice", "price": 68, "category": "飲料", "name_jp": "ミニッツメイド® オレンジ(S)", "price_jp": 240, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/33443.png?"}, {"id": "72", "name_zh": "經典卡布奇諾", "name_en": "Classic Cappuccino", "price": 77, "category": "飲料", "name_jp": "カプチーノ", "price_jp": 290, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/3093.png?"}, {"id": "71", "name_zh": "經典拿鐵", "name_en": "Classic Latte", "price": 77, "category": "飲料", "name_jp": "カフェラテ", "price_jp": 290, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/3081.png?"}, {"id": "70", "name_zh": "經典美式咖啡", "name_en": "Classic Americano", "price": 50, "category": "飲料", "name_jp": "プレミアムローストコーヒー", "price_jp": 120, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/3080.png?"}, {"id": "57", "name_zh": "雪碧 (中)", "name_en": "Sprite (M)", "price": 38, "category": "飲料", "name_jp": "スプライト (中)", "price_jp": 150, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/3194.png?"}, {"id": "56", "name_zh": "雪碧 (小)", "name_en": "Sprite (S)", "price": 33, "category": "飲料", "name_jp": "スプライト (小)", "price_jp": 140, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/3194.png?"}, {"id": "55", "name_zh": "零卡可口可樂 (中)", "name_en": "Coca-Cola Zero (M)", "price": 38, "category": "飲料", "name_jp": "コカ・コーラ ゼロ (中)", "price_jp": 150, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/3189.png?"}, {"id": "54", "name_zh": "零卡可口可樂 (小)", "name_en": "Coca-Cola Zero (S)", "price": 33, "category": "飲料", "name_jp": "コカ・コーラ ゼロ (小)", "price_jp": 140, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/3189.png?"}, {"id": "68", "name_zh": "鮮奶", "name_en": "Fresh Milk", "price": 33, "category": "飲料", "name_jp": "ミルク", "price_jp": 270, "image_url": "https://www.mcdelivery.com.tw/tw//static/1749020058820/assets/886/products/3508.png?"}]}]}, "metadata": {"source": "bdd", "generatedAt": "2025-06-06T01:46:12.681Z", "aiGenerated": true}}