import express from 'express';
import cors from 'cors';
import multer from 'multer';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
import dotenv from 'dotenv';
import fs from 'node:fs';
import { MenuProcessor } from './services/MenuProcessor.js';
import { FirebaseService } from './services/FirebaseService.js';
import nlpRoutes from './routes/nlp.js';
import { updateLoadedMenu } from './routes/nlp.js'; // 導入菜單同步函數
import promptRoutes from './routes/prompt.js';
import { PromptEngine } from './services/PromptEngine.js';
import orderRoutes from './routes/order.js'; // 新增訂單路由
// import jsonTestRoutes from './routes/json-test.js'; // 新增 JSON 測試路由 - 已移除
import filesRoutes from './routes/files.js'; // 新增文件操作路由
import { jsonParserFix } from './middleware/json-parser-fix.js'; // 新增 JSON 解析修復中間件

// 載入環境變數
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 確保 uploads 目錄存在
const uploadsDir = path.join(__dirname, '../uploads');
if (!fs.existsSync(uploadsDir)) {
  try {
    fs.mkdirSync(uploadsDir, { recursive: true, mode: 0o755 });
    console.log('已成功創建 uploads 目錄');
  } catch (error) {
    console.error('創建 uploads 目錄失敗:', error);
    process.exit(1); // 如果無法創建上傳目錄，則終止程序
  }
}

// 確保 appPrompt 目錄存在
const appPromptDir = path.join(__dirname, '../appPrompt');
if (!fs.existsSync(appPromptDir)) {
  try {
    fs.mkdirSync(appPromptDir, { recursive: true, mode: 0o755 });
    console.log('已成功創建 appPrompt 目錄');
  } catch (error) {
    console.error('創建 appPrompt 目錄失敗:', error);
    process.exit(1); // 如果無法創建目錄，則終止程序
  }
}

const app = express();
// 根據環境設定端口：開發環境使用 3004，生產環境使用 3005
const port = process.env.PORT || (process.env.NODE_ENV === 'production' ? 3005 : 3004);
const menuProcessor = new MenuProcessor();
const promptEngine = new PromptEngine();

// 初始化 Firebase (如果配置了環境變數)
let firebaseService: FirebaseService | null = null;

if (process.env.FIREBASE_API_KEY && process.env.FIREBASE_PROJECT_ID) {
  try {
    firebaseService = new FirebaseService({
      apiKey: process.env.FIREBASE_API_KEY,
      authDomain: `${process.env.FIREBASE_PROJECT_ID}.firebaseapp.com`,
      projectId: process.env.FIREBASE_PROJECT_ID,
      storageBucket: process.env.FIREBASE_STORAGE_BUCKET || `${process.env.FIREBASE_PROJECT_ID}.appspot.com`,
      messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID || '',
      appId: process.env.FIREBASE_APP_ID || ''
    });
    
    console.log('已成功初始化 Firebase');
  } catch (error) {
    console.error('Firebase 初始化失敗:', error);
  }
}

// 中間件
app.use(cors());
app.use(express.json());
app.use(jsonParserFix); // 添加 JSON 解析修復中間件

// 配置文件上傳
const storage = multer.diskStorage({
  destination: function(req, file, cb) {
    cb(null, path.join(__dirname, '../uploads/'));
  },
  filename: function(req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, uniqueSuffix + '-' + file.originalname);
  }
});

const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  },
  fileFilter: function(req, file, cb) {
    const filetypes = /csv|xlsx|json/;
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = filetypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('只支援 CSV、Excel 和 JSON 檔案格式'));
    }
  }
});

// 導入 API 路由
app.use('/api/nlp', nlpRoutes);
// app.use('/api/json-test', jsonTestRoutes); // 新增 JSON 測試路由 - 已移除
app.use('/api/files', filesRoutes); // 新增文件操作路由
// 將 promptEngine 傳遞給 prompt 路由
app.use('/api/prompt', (req, res, next) => {
  (req as any).promptEngine = promptEngine;
  next();
}, promptRoutes);
app.use('/api/order', orderRoutes); // 註冊訂單路由

// 靜態文件中間件 (提供網頁界面)
app.use(express.static(path.join(__dirname, '../public')));

// 首頁路由 (當 public 中沒有 index.html 時的備用路由)
app.get('/', (req, res) => {
  const indexPath = path.join(__dirname, '../public/index.html');
  if (fs.existsSync(indexPath)) {
    res.sendFile(indexPath);
  } else {
    res.send('自然語言點餐系統 API 服務運行中 - 前端頁面未找到');
  }
});

// 開發版頁面路由
app.get('/dev', (req, res) => {
  const devPath = path.join(__dirname, '../public/dev.html');
  if (fs.existsSync(devPath)) {
    res.sendFile(devPath);
  } else {
    res.status(404).send('開發版頁面未找到');
  }
});

// 檔案自動清理程序（24小時後）
const cleanupUploadedFiles = async (filePath: string) => {
  try {
    // 等待 24 小時
    await new Promise(resolve => setTimeout(resolve, 24 * 60 * 60 * 1000));
    
    // 檢查檔案是否仍然存在
    if (fs.existsSync(filePath)) {
      await fs.promises.unlink(filePath);
      console.log(`自動清理：已刪除過期檔案 ${filePath}`);
    }
  } catch (error) {
    console.error('自動清理檔案時發生錯誤:', error);
  }
};

// 菜單上傳 API
app.post('/api/menu/upload', upload.single('file'), (req: express.Request, res: express.Response): void => {
  (async () => {
    const startTime = Date.now();
    let filePath: string | undefined;
    
    try {
      if (!req.file) {
        res.status(400).json({ 
          success: false, 
          errors: ['請上傳檔案'],
          details: {
            allowedTypes: ['csv', 'xlsx', 'json'],
            maxSize: '10MB'
          }
        });
        return;
      }

      filePath = req.file.path;
      console.log(`開始處理上傳的檔案: ${req.file.originalname}`);
      
      const restaurantId = req.body.restaurant_id || 'default_restaurant';
      const options = {
        autoDetectCategory: req.body.auto_detect_category === 'true',
        skipValidation: req.body.skip_validation === 'true'
      };

      // 檢查檔案是否存在
      if (!fs.existsSync(filePath)) {
        throw new Error('上傳的檔案未能正確保存');
      }

      console.log(`處理菜單檔案: ${filePath}`);
      const result = await menuProcessor.processMenuFile(filePath, restaurantId, options);
        if (result.success && result.data) {
        // 將菜單數據設置到 PromptEngine 中
        promptEngine.setMenuData(result.data);
        console.log('已將菜單數據設置到 PromptEngine 中供生成 APPprompt 使用');
        
        // 同步菜單數據到 NLP 路由
        const syncSuccess = updateLoadedMenu(restaurantId, result.data);
        if (syncSuccess) {
          console.log(`已將菜單同步到 NLP 服務，餐廳 ID: ${restaurantId}`);
        } else {
          console.warn(`菜單同步到 NLP 服務失敗，餐廳 ID: ${restaurantId}`);
        }
        
        // 添加更詳細的成功訊息
        const response = {
          ...result,
          message: '菜單已成功上傳並處理',
          details: {
            fileName: req.file.originalname,
            fileSize: req.file.size,
            processingTime: Date.now() - startTime,
            restaurantId: restaurantId,
            itemCount: result.data.categories.reduce((sum, cat) => sum + cat.items.length, 0),
            categories: result.data.categories.map(cat => ({
              name_zh: cat.name_zh,
              name_en: cat.name_en,
              name_jp: cat.name_jp,
              itemCount: cat.items.length
            }))
          }
        };

        // 啟動自動清理程序（24小時後）
        cleanupUploadedFiles(filePath);

        res.json(response);
      } else {
        // 失敗也保留檔案24小時以便排查
        cleanupUploadedFiles(filePath);

        res.status(400).json({
          success: false,
          errors: result.errors || ['處理菜單檔案時發生未知錯誤'],
          warnings: result.warnings,
          details: {
            fileName: req.file.originalname,
            fileSize: req.file.size,
            processingTime: Date.now() - startTime
          }
        });
      }
    } catch (error) {
      console.error('處理上傳檔案時發生錯誤:', error);

      if (filePath && fs.existsSync(filePath)) {
        // 錯誤情況下也保留檔案24小時
        cleanupUploadedFiles(filePath);
      }

      res.status(500).json({
        success: false,
        errors: [error instanceof Error ? error.message : '處理上傳檔案時發生未知錯誤'],
        details: {
          fileName: req.file?.originalname,
          processingTime: Date.now() - startTime
        }
      });
    }
  })();
});

app.listen(port, () => {
  console.log(`伺服器運行在 http://localhost:${port}`);
});
